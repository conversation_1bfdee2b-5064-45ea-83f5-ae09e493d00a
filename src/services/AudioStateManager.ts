/**
 * Centralized Audio State Manager
 * Eliminates race conditions and global state issues
 */

export type AudioState = 'idle' | 'loading' | 'playing' | 'ended' | 'error';

export interface AudioStateData {
  state: AudioState;
  currentAudio: HTMLAudioElement | null;
  duration: number;
  currentTime: number;
  error: string | null;
}

export type AudioStateListener = (state: AudioStateData) => void;

class AudioStateManager {
  private state: AudioState = 'idle';
  private currentAudio: HTMLAudioElement | null = null;
  private listeners: Set<AudioStateListener> = new Set();
  private timeoutId: NodeJS.Timeout | null = null;
  private callbackExecuted = false;

  // Callbacks
  private onEndedCallback?: () => void;
  private onStartedCallback?: () => void;
  private onErrorCallback?: (error: string) => void;

  /**
   * Get current audio state data
   */
  getState(): AudioStateData {
    return {
      state: this.state,
      currentAudio: this.currentAudio,
      duration: this.currentAudio?.duration || 0,
      currentTime: this.currentAudio?.currentTime || 0,
      error: null
    };
  }

  /**
   * Check if audio is currently playing
   */
  isPlaying(): boolean {
    if (!this.currentAudio) return false;
    
    return !this.currentAudio.paused &&
           !this.currentAudio.ended &&
           this.currentAudio.readyState > 2 &&
           this.currentAudio.currentTime > 0;
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: AudioStateListener): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of state change
   */
  private notifyListeners(): void {
    const stateData = this.getState();
    this.listeners.forEach(listener => listener(stateData));
  }

  /**
   * Update state and notify listeners
   */
  private setState(newState: AudioState): void {
    if (this.state !== newState) {
      console.log(`🔊 [AudioManager] State: ${this.state} → ${newState}`);
      this.state = newState;
      this.notifyListeners();
    }
  }

  /**
   * Execute callback only once
   */
  private executeCallback(source: string): void {
    if (!this.callbackExecuted && this.onEndedCallback) {
      this.callbackExecuted = true;
      console.log(`🎤 [AudioManager] Callback executed from: ${source}`);
      this.clearTimeout();
      this.onEndedCallback();
    }
  }

  /**
   * Clear any pending timeout
   */
  private clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * Setup audio event listeners
   */
  private setupAudioListeners(audio: HTMLAudioElement): void {
    // Playing event - audio actually started
    audio.addEventListener('playing', () => {
      console.log('🔊 [AudioManager] Audio started playing');
      this.setState('playing');
      if (this.onStartedCallback) {
        this.onStartedCallback();
      }
    });

    // Ended event - audio finished naturally
    audio.addEventListener('ended', () => {
      console.log('🔊 [AudioManager] Audio ended naturally');
      this.setState('ended');
      this.executeCallback('ended-event');
    });

    // Error event
    audio.addEventListener('error', (e) => {
      console.error('❌ [AudioManager] Audio error:', e);
      this.setState('error');
      this.executeCallback('error-fallback');
      if (this.onErrorCallback) {
        this.onErrorCallback(`Audio error: ${e.type}`);
      }
    });

    // Metadata loaded - setup safety timeout
    audio.addEventListener('loadedmetadata', () => {
      const duration = audio.duration || 10;
      const timeoutDuration = (duration + 3) * 1000;

      console.log(`⏱️ [AudioManager] Setting timeout: ${timeoutDuration}ms for ${duration}s audio`);

      this.timeoutId = setTimeout(() => {
        if (!this.callbackExecuted && this.state === 'playing') {
          console.log('⏰ [AudioManager] Safety timeout reached');
          this.executeCallback('timeout-fallback');
        }
      }, timeoutDuration);
    });
  }

  /**
   * Play audio with proper state management
   */
  async playAudio(
    audioUrl: string,
    onEnded?: () => void,
    onStarted?: () => void,
    onError?: (error: string) => void
  ): Promise<void> {
    // Stop any current audio
    this.stopAudio();

    // Reset state
    this.callbackExecuted = false;
    this.onEndedCallback = onEnded;
    this.onStartedCallback = onStarted;
    this.onErrorCallback = onError;

    try {
      this.setState('loading');
      
      const audio = new Audio(audioUrl);
      audio.preload = 'auto';
      audio.volume = 0.8;

      this.currentAudio = audio;
      this.setupAudioListeners(audio);

      await audio.play();
      console.log('✅ [AudioManager] Audio play initiated');

    } catch (error) {
      console.warn('⚠️ [AudioManager] Error starting playback:', error);
      this.setState('error');

      // Handle AbortError specially
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('⏳ [AudioManager] AbortError - waiting to see if audio recovers...');
        
        // Give audio time to recover, then check if it actually started
        setTimeout(() => {
          if (this.state !== 'playing' && !this.callbackExecuted) {
            console.log('⏰ [AudioManager] AbortError confirmed - audio never started');
            this.executeCallback('abort-confirmed');
          }
        }, 3000);
      } else {
        // For other errors, execute callback after short delay
        setTimeout(() => {
          if (!this.callbackExecuted) {
            this.executeCallback('error-timeout-fallback');
          }
        }, 1000);

        if (this.onErrorCallback) {
          this.onErrorCallback(`Playback error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    }
  }

  /**
   * Stop current audio
   */
  stopAudio(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
      console.log('🛑 [AudioManager] Audio stopped');
    }

    this.clearTimeout();
    this.callbackExecuted = true; // Prevent any pending callbacks
    this.setState('idle');
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopAudio();
    this.listeners.clear();
  }
}

// Singleton instance
export const audioStateManager = new AudioStateManager();
