/**
 * Integration tests for the complete conversation flow
 * Verifies that the timing fixes eliminate race conditions in real scenarios
 */

import { audioStateManager } from '../services/AudioStateManager';
import { conversationStateManager } from '../services/ConversationStateManager';
import { conversationStateMachine } from '../services/ConversationStateMachine';

// Mock dependencies
jest.mock('../stores/conversationStore', () => ({
  useConversationStore: {
    getState: () => ({
      conversationState: 'idle',
      isActive: false,
      isListening: false,
      smartMicrophoneEnabled: true,
      setConversationState: jest.fn(),
      setIsActive: jest.fn(),
      setIsListening: jest.fn(),
      getShouldBeListening: () => true
    })
  }
}));

// Mock HTMLAudioElement
class MockAudioElement {
  public paused = true;
  public ended = false;
  public readyState = 0;
  public currentTime = 0;
  public duration = 5;
  public volume = 1;
  public preload = 'none';
  private listeners: { [key: string]: Function[] } = {};

  addEventListener(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  dispatchEvent(event: string, data?: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }

  async play() {
    this.paused = false;
    this.readyState = 4;
    setTimeout(() => this.dispatchEvent('loadedmetadata'), 5);
    setTimeout(() => this.dispatchEvent('playing'), 10);
    return Promise.resolve();
  }

  pause() {
    this.paused = true;
  }

  simulateEnd() {
    this.ended = true;
    this.dispatchEvent('ended');
  }
}

(global as any).Audio = jest.fn().mockImplementation(() => new MockAudioElement());

describe('Conversation Flow Integration', () => {
  beforeEach(() => {
    // Reset all managers
    audioStateManager.stopAudio();
    conversationStateManager.reset();
    conversationStateMachine.reset();
    jest.clearAllMocks();
  });

  afterEach(() => {
    audioStateManager.stopAudio();
    conversationStateManager.cancelPendingOperations();
  });

  describe('Audio Playback Flow', () => {
    test('should handle complete audio playback cycle without race conditions', async () => {
      const events: string[] = [];
      
      // Subscribe to audio state changes
      audioStateManager.subscribe((state) => {
        events.push(`audio:${state.state}`);
      });

      // Subscribe to conversation state changes
      conversationStateManager.subscribe((state) => {
        events.push(`conversation:${state.state}`);
      });

      // Start audio playback
      let audioFinishedCallbackExecuted = false;
      await audioStateManager.playAudio(
        'test-audio.mp3',
        () => {
          audioFinishedCallbackExecuted = true;
          events.push('callback:audioFinished');
        },
        () => {
          events.push('callback:audioStarted');
        }
      );

      // Wait for audio to start
      await new Promise(resolve => setTimeout(resolve, 15));

      // Simulate audio ending
      const mockAudio = audioStateManager.getState().currentAudio as MockAudioElement;
      mockAudio?.simulateEnd();

      // Wait for callbacks to execute
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(audioFinishedCallbackExecuted).toBe(true);
      expect(events).toContain('audio:loading');
      expect(events).toContain('audio:playing');
      expect(events).toContain('audio:ended');
      expect(events).toContain('callback:audioStarted');
      expect(events).toContain('callback:audioFinished');
    });

    test('should prevent duplicate callbacks in rapid audio cycles', async () => {
      let callbackCount = 0;
      
      // Start multiple audio plays rapidly
      const promises = Array.from({ length: 5 }, (_, i) =>
        audioStateManager.playAudio(
          `test-audio-${i}.mp3`,
          () => callbackCount++
        )
      );

      await Promise.all(promises);

      // Only one audio should be active, preventing multiple callbacks
      expect(callbackCount).toBeLessThanOrEqual(1);
    });
  });

  describe('State Machine Integration', () => {
    test('should enforce valid conversation state transitions', async () => {
      const invalidTransitions: string[] = [];
      
      // Create state machine with invalid transition tracking
      conversationStateMachine.reset();
      
      // Try valid transition
      const validResult = await conversationStateMachine.transitionTo('listening');
      expect(validResult).toBe(true);
      expect(conversationStateMachine.getCurrentState()).toBe('listening');

      // Try another valid transition
      const validResult2 = await conversationStateMachine.transitionTo('processing');
      expect(validResult2).toBe(true);
      expect(conversationStateMachine.getCurrentState()).toBe('processing');
    });

    test('should handle concurrent state transitions safely', async () => {
      // Start multiple state transitions simultaneously
      const promises = [
        conversationStateMachine.transitionTo('listening'),
        conversationStateMachine.transitionTo('speaking'),
        conversationStateMachine.transitionTo('processing')
      ];

      const results = await Promise.all(promises);
      
      // Only one should succeed due to race condition protection
      const successCount = results.filter(Boolean).length;
      expect(successCount).toBe(1);
    });
  });

  describe('Auto-Activation Flow', () => {
    test('should handle auto-activation after first audio without race conditions', async () => {
      const events: string[] = [];
      let conversationActivated = false;
      let microphoneActivated = false;

      // Set up conversation state manager
      conversationStateManager.setCanAutoActivate(true);
      conversationStateManager.setActivationHandlers(
        async () => {
          conversationActivated = true;
          events.push('conversation:activated');
          return true;
        },
        () => {
          microphoneActivated = true;
          events.push('microphone:activated');
        }
      );

      // Simulate audio finishing (which should trigger auto-activation)
      conversationStateManager.setConversationState('speaking');
      await new Promise(resolve => setTimeout(resolve, 10));
      
      conversationStateManager.setConversationState('idle');
      
      // Wait for auto-activation logic to execute
      await new Promise(resolve => setTimeout(resolve, 1100));

      expect(events).toContain('conversation:activated');
    });

    test('should prevent multiple auto-activations', async () => {
      let activationCount = 0;

      conversationStateManager.setCanAutoActivate(true);
      conversationStateManager.setActivationHandlers(
        async () => {
          activationCount++;
          return true;
        },
        () => {}
      );

      // Trigger multiple state changes that could cause auto-activation
      for (let i = 0; i < 5; i++) {
        conversationStateManager.setConversationState('speaking');
        conversationStateManager.setConversationState('idle');
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Wait for any pending activations
      await new Promise(resolve => setTimeout(resolve, 1200));

      // Should only activate once
      expect(activationCount).toBeLessThanOrEqual(1);
    });
  });

  describe('Error Recovery', () => {
    test('should recover gracefully from audio errors', async () => {
      let errorCallbackExecuted = false;
      let endCallbackExecuted = false;

      await audioStateManager.playAudio(
        'test-audio.mp3',
        () => { endCallbackExecuted = true; },
        undefined,
        () => { errorCallbackExecuted = true; }
      );

      // Simulate audio error
      const mockAudio = audioStateManager.getState().currentAudio as MockAudioElement;
      mockAudio?.dispatchEvent('error', { type: 'network' });

      await new Promise(resolve => setTimeout(resolve, 10));

      expect(errorCallbackExecuted).toBe(true);
      expect(endCallbackExecuted).toBe(false); // End callback should not execute on error
    });

    test('should handle state machine errors gracefully', async () => {
      // Force an error in state transition
      const originalTransition = conversationStateMachine.transitionTo;
      conversationStateMachine.transitionTo = jest.fn().mockRejectedValue(new Error('Test error'));

      // Should not crash the application
      const result = await conversationStateMachine.transitionTo('listening');
      expect(result).toBe(false);

      // Restore original method
      conversationStateMachine.transitionTo = originalTransition;
    });
  });

  describe('Memory Management', () => {
    test('should clean up resources properly', () => {
      // Create subscriptions
      const audioUnsubscribe = audioStateManager.subscribe(() => {});
      const conversationUnsubscribe = conversationStateManager.subscribe(() => {});

      // Start audio
      audioStateManager.playAudio('test-audio.mp3');

      // Clean up
      audioUnsubscribe();
      conversationUnsubscribe();
      audioStateManager.destroy();
      conversationStateManager.destroy();

      // Should not have any active audio or pending operations
      expect(audioStateManager.getState().currentAudio).toBeNull();
      expect(audioStateManager.getState().state).toBe('idle');
    });
  });

  describe('Timing Scenarios', () => {
    test('should handle rapid start/stop cycles', async () => {
      const events: string[] = [];

      for (let i = 0; i < 10; i++) {
        audioStateManager.playAudio(`audio-${i}.mp3`, () => {
          events.push(`ended-${i}`);
        });
        
        if (i % 2 === 0) {
          audioStateManager.stopAudio();
        }
        
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // Should handle this gracefully without crashes or excessive callbacks
      expect(events.length).toBeLessThan(10); // Some callbacks should be prevented
    });

    test('should handle delayed audio loading', async () => {
      let callbackExecuted = false;

      // Mock slow audio loading
      const slowMockAudio = new MockAudioElement();
      slowMockAudio.play = async function() {
        this.paused = false;
        this.readyState = 4;
        
        // Simulate slow loading
        setTimeout(() => this.dispatchEvent('loadedmetadata'), 100);
        setTimeout(() => this.dispatchEvent('playing'), 200);
        
        return Promise.resolve();
      };

      (global as any).Audio = jest.fn().mockImplementation(() => slowMockAudio);

      await audioStateManager.playAudio(
        'slow-audio.mp3',
        () => { callbackExecuted = true; }
      );

      // Should handle slow loading without timing issues
      await new Promise(resolve => setTimeout(resolve, 250));
      
      expect(audioStateManager.getState().state).toBe('playing');
    });
  });
});
