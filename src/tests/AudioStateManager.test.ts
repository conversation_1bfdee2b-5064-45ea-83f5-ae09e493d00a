/**
 * Tests for AudioStateManager
 * Verifies that audio state management eliminates race conditions
 */

import { audioStateManager } from '../services/AudioStateManager';

// Mock HTMLAudioElement
class MockAudioElement {
  public paused = true;
  public ended = false;
  public readyState = 0;
  public currentTime = 0;
  public duration = 10;
  public volume = 1;
  public preload = 'none';
  private listeners: { [key: string]: Function[] } = {};

  addEventListener(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  removeEventListener(event: string, callback: Function) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }

  dispatchEvent(event: string, data?: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }

  async play() {
    this.paused = false;
    this.readyState = 4;
    this.dispatchEvent('loadedmetadata');
    setTimeout(() => {
      this.dispatchEvent('playing');
    }, 10);
    return Promise.resolve();
  }

  pause() {
    this.paused = true;
  }

  end() {
    this.ended = true;
    this.dispatchEvent('ended');
  }

  error(errorType = 'error') {
    this.dispatchEvent('error', { type: errorType });
  }
}

// Mock global Audio constructor
(global as any).Audio = jest.fn().mockImplementation(() => new MockAudioElement());

describe('AudioStateManager', () => {
  beforeEach(() => {
    // Reset audio state manager
    audioStateManager.stopAudio();
    jest.clearAllMocks();
  });

  afterEach(() => {
    audioStateManager.stopAudio();
  });

  describe('State Management', () => {
    test('should start in idle state', () => {
      const state = audioStateManager.getState();
      expect(state.state).toBe('idle');
      expect(state.currentAudio).toBeNull();
    });

    test('should transition to loading when playing audio', async () => {
      const stateChanges: string[] = [];
      
      audioStateManager.subscribe((state) => {
        stateChanges.push(state.state);
      });

      await audioStateManager.playAudio('test-url.mp3');
      
      expect(stateChanges).toContain('loading');
    });

    test('should transition to playing when audio starts', async () => {
      const stateChanges: string[] = [];
      
      audioStateManager.subscribe((state) => {
        stateChanges.push(state.state);
      });

      await audioStateManager.playAudio('test-url.mp3');
      
      // Wait for playing event
      await new Promise(resolve => setTimeout(resolve, 20));
      
      expect(stateChanges).toContain('playing');
    });

    test('should transition to ended when audio finishes', async () => {
      const stateChanges: string[] = [];
      let endedCallbackExecuted = false;
      
      audioStateManager.subscribe((state) => {
        stateChanges.push(state.state);
      });

      await audioStateManager.playAudio(
        'test-url.mp3',
        () => { endedCallbackExecuted = true; }
      );

      // Simulate audio ending
      const mockAudio = audioStateManager.getState().currentAudio as MockAudioElement;
      mockAudio?.end();

      expect(stateChanges).toContain('ended');
      expect(endedCallbackExecuted).toBe(true);
    });
  });

  describe('Callback Execution', () => {
    test('should execute onEnded callback only once', async () => {
      let callbackCount = 0;
      
      await audioStateManager.playAudio(
        'test-url.mp3',
        () => { callbackCount++; }
      );

      const mockAudio = audioStateManager.getState().currentAudio as MockAudioElement;
      
      // Trigger multiple end events
      mockAudio?.end();
      mockAudio?.end();
      mockAudio?.end();

      expect(callbackCount).toBe(1);
    });

    test('should execute onStarted callback when audio starts playing', async () => {
      let startedCallbackExecuted = false;
      
      await audioStateManager.playAudio(
        'test-url.mp3',
        undefined,
        () => { startedCallbackExecuted = true; }
      );

      // Wait for playing event
      await new Promise(resolve => setTimeout(resolve, 20));
      
      expect(startedCallbackExecuted).toBe(true);
    });

    test('should execute onError callback on audio error', async () => {
      let errorCallbackExecuted = false;
      let errorMessage = '';
      
      await audioStateManager.playAudio(
        'test-url.mp3',
        undefined,
        undefined,
        (error) => { 
          errorCallbackExecuted = true;
          errorMessage = error;
        }
      );

      const mockAudio = audioStateManager.getState().currentAudio as MockAudioElement;
      mockAudio?.error('network');

      expect(errorCallbackExecuted).toBe(true);
      expect(errorMessage).toContain('Audio error');
    });
  });

  describe('Race Condition Prevention', () => {
    test('should handle rapid play/stop cycles without race conditions', async () => {
      const callbacks: string[] = [];
      
      // Start multiple audio plays rapidly
      const promises = [
        audioStateManager.playAudio('url1.mp3', () => callbacks.push('ended1')),
        audioStateManager.playAudio('url2.mp3', () => callbacks.push('ended2')),
        audioStateManager.playAudio('url3.mp3', () => callbacks.push('ended3'))
      ];

      await Promise.all(promises);

      // Only the last audio should be active
      const state = audioStateManager.getState();
      expect(state.currentAudio).toBeTruthy();
      
      // Stop audio
      audioStateManager.stopAudio();
      
      // Should prevent any pending callbacks
      expect(callbacks.length).toBeLessThanOrEqual(1);
    });

    test('should prevent callbacks after stopAudio is called', async () => {
      let callbackExecuted = false;
      
      await audioStateManager.playAudio(
        'test-url.mp3',
        () => { callbackExecuted = true; }
      );

      // Stop audio immediately
      audioStateManager.stopAudio();

      // Try to trigger ended event
      const state = audioStateManager.getState();
      expect(state.currentAudio).toBeNull();
      expect(callbackExecuted).toBe(false);
    });
  });

  describe('Audio State Detection', () => {
    test('should correctly detect when audio is playing', async () => {
      expect(audioStateManager.isPlaying()).toBe(false);
      
      await audioStateManager.playAudio('test-url.mp3');
      
      // Wait for playing event
      await new Promise(resolve => setTimeout(resolve, 20));
      
      expect(audioStateManager.isPlaying()).toBe(true);
    });

    test('should correctly detect when audio is not playing', () => {
      audioStateManager.stopAudio();
      expect(audioStateManager.isPlaying()).toBe(false);
    });
  });

  describe('Cleanup', () => {
    test('should clean up resources when destroyed', () => {
      const unsubscribe = audioStateManager.subscribe(() => {});
      
      audioStateManager.destroy();
      
      const state = audioStateManager.getState();
      expect(state.currentAudio).toBeNull();
      expect(state.state).toBe('idle');
    });
  });

  describe('Error Handling', () => {
    test('should handle AbortError gracefully', async () => {
      const mockAudio = new MockAudioElement();
      mockAudio.play = jest.fn().mockRejectedValue(new Error('AbortError'));
      (global as any).Audio = jest.fn().mockImplementation(() => mockAudio);

      let errorCallbackExecuted = false;
      
      await audioStateManager.playAudio(
        'test-url.mp3',
        undefined,
        undefined,
        () => { errorCallbackExecuted = true; }
      );

      // Should handle the error gracefully
      expect(errorCallbackExecuted).toBe(true);
    });
  });
});
