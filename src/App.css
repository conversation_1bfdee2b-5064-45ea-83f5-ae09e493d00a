#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Game Container Styles */
.game-container {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
}

.game-title {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

/* Quick Start Section */
.quick-start-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #e8f5e8;
  border-radius: 12px;
  text-align: center;
  border: 2px solid #28a745;
}

.quick-start-title {
  color: #155724;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.quick-start-description {
  color: #155724;
  margin-bottom: 20px;
  line-height: 1.5;
}

.primary-button {
  padding: 15px 30px;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.primary-button:enabled {
  background-color: #28a745;
}

.primary-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.primary-button:hover:enabled {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

/* Reset Button */
.reset-section {
  margin-top: 20px;
  text-align: center;
}

.reset-button {
  padding: 8px 16px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-button:hover {
  background-color: #5a6268;
  transform: translateY(-1px);
}

/* Voice Chat Container Styles */
.voice-chat-container {
  background-color: #ffffff;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 24px;
  margin-top: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.character-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #343a40;
}

.status-indicator {
  text-align: center;
  margin-top: 16px;
  font-size: 12px;
  color: #6c757d;
}

.debug-info {
  text-align: center;
  margin-top: 8px;
  font-size: 10px;
  color: #999;
  font-family: monospace;
}

/* Error Message Styles */
.error-message {
  background-color: #f8d7da;
  border: 2px solid #dc3545;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 20px;
  color: #721c24;
  font-size: 14px;
  text-align: center;
}

/* Voice Button Container */
.voice-button-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}
